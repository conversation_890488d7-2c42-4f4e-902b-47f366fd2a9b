name: Auto Commit Changes

on:
  workflow_dispatch:
    inputs:
      commit_message:
        description: 'Commit message'
        required: true
        default: 'chore: update configuration'

jobs:
  auto-commit:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          fetch-depth: 0

      - name: Configure Git
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"

      - name: Check for changes
        id: verify-changed-files
        run: |
          if [ -n "$(git status --porcelain)" ]; then
            echo "changed=true" >> $GITHUB_OUTPUT
          else
            echo "changed=false" >> $GITHUB_OUTPUT
          fi

      - name: Commit changes
        if: steps.verify-changed-files.outputs.changed == 'true'
        run: |
          git add .
          git commit -m "${{ github.event.inputs.commit_message }}"
          git push origin main

      - name: No changes detected
        if: steps.verify-changed-files.outputs.changed == 'false'
        run: echo "No changes detected, skipping commit."
