name: Build & Deploy Hexo Blog

on:
  push:
    branches: ["main", "master"]
  workflow_dispatch:

env:
  SSH_HOST: ${{ secrets.SSH_HOST }}
  SSH_USER: ${{ secrets.SSH_USER }}
  SSH_PORT: ${{ secrets.SSH_PORT }}
  TARGET_DIR: ${{ secrets.TARGET_DIR }}

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: false

      # 检测包管理器
      - name: Detect package manager
        id: pm
        run: |
          cd anzhiyu-blog
          if [ -f pnpm-lock.yaml ]; then echo "pm=pnpm" >> $GITHUB_OUTPUT
          elif [ -f yarn.lock ]; then echo "pm=yarn" >> $GITHUB_OUTPUT
          elif [ -f package-lock.json ]; then echo "pm=npm" >> $GITHUB_OUTPUT
          else echo "pm=npm" >> $GITHUB_OUTPUT; fi

      # 设置 Node.js 环境
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: ${{ steps.pm.outputs.pm }}
          cache-dependency-path: 'anzhiyu-blog/package-lock.json'

      # 安装依赖和构建
      - name: Install dependencies and build
        run: |
          set -e
          cd anzhiyu-blog

          # 安装依赖
          if [ "${{ steps.pm.outputs.pm }}" = "pnpm" ]; then
            npm install -g pnpm
            pnpm install --frozen-lockfile
          elif [ "${{ steps.pm.outputs.pm }}" = "yarn" ]; then
            yarn install --frozen-lockfile
          else
            npm ci
          fi

          # 构建 Hexo 博客
          if [ "${{ steps.pm.outputs.pm }}" = "pnpm" ]; then
            pnpm run clean
            pnpm run build
          elif [ "${{ steps.pm.outputs.pm }}" = "yarn" ]; then
            yarn clean
            yarn build
          else
            npm run clean
            npm run build
          fi

          # 检查构建输出目录
          if [ -d "public" ]; then
            echo "OUT_DIR=anzhiyu-blog/public" >> $GITHUB_ENV
          else
            echo "Build output directory not found!"
            exit 1
          fi

      # 部署到服务器
      - name: Deploy to server
        uses: burnett01/rsync-deployments@6.0.0
        with:
          switches: -avzr --delete --exclude='.git*' --exclude='node_modules' --exclude='.github'
          path: ${{ env.OUT_DIR }}/
          remote_path: ${{ secrets.TARGET_DIR }}
          remote_host: ${{ secrets.SSH_HOST }}
          remote_port: ${{ secrets.SSH_PORT }}
          remote_user: ${{ secrets.SSH_USER }}
          remote_key: ${{ secrets.SSH_PRIVATE_KEY }}
