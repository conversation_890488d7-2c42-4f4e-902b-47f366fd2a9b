name: Build & Deploy Hexo Blog

on:
  push:
    branches: ["main", "master"]
  workflow_dispatch:

env:
  SSH_HOST: ${{ secrets.SSH_HOST }}
  SSH_USER: ${{ secrets.SSH_USER }}
  SSH_PORT: ${{ secrets.SSH_PORT }}
  TARGET_DIR: ${{ secrets.TARGET_DIR }}

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          submodules: false
          fetch-depth: 0

      # 检测包管理器
      - name: Detect package manager
        id: pm
        run: |
          cd anzhiyu-blog
          if [ -f pnpm-lock.yaml ]; then
            echo "pm=pnpm" >> $GITHUB_OUTPUT
            echo "cache-path=anzhiyu-blog/pnpm-lock.yaml" >> $GITHUB_OUTPUT
          elif [ -f yarn.lock ]; then
            echo "pm=yarn" >> $GITHUB_OUTPUT
            echo "cache-path=anzhiyu-blog/yarn.lock" >> $GITHUB_OUTPUT
          elif [ -f package-lock.json ]; then
            echo "pm=npm" >> $GITHUB_OUTPUT
            echo "cache-path=anzhiyu-blog/package-lock.json" >> $GITHUB_OUTPUT
          else
            echo "pm=npm" >> $GITHUB_OUTPUT
            echo "cache-path=anzhiyu-blog/package.json" >> $GITHUB_OUTPUT
          fi

      # 设置 Node.js 环境
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: ${{ steps.pm.outputs.pm }}
          cache-dependency-path: ${{ steps.pm.outputs.cache-path }}

      # 安装依赖
      - name: Install dependencies
        run: |
          set -e
          cd anzhiyu-blog

          echo "📦 Installing dependencies with ${{ steps.pm.outputs.pm }}..."

          if [ "${{ steps.pm.outputs.pm }}" = "pnpm" ]; then
            npm install -g pnpm
            pnpm install --frozen-lockfile
          elif [ "${{ steps.pm.outputs.pm }}" = "yarn" ]; then
            yarn install --frozen-lockfile
          else
            npm ci
          fi

      # 构建博客
      - name: Build Hexo blog
        run: |
          set -e
          cd anzhiyu-blog

          echo "🏗️ Building Hexo blog..."

          # 清理缓存
          if [ "${{ steps.pm.outputs.pm }}" = "pnpm" ]; then
            pnpm run clean
          elif [ "${{ steps.pm.outputs.pm }}" = "yarn" ]; then
            yarn clean
          else
            npm run clean
          fi

          # 构建静态文件
          if [ "${{ steps.pm.outputs.pm }}" = "pnpm" ]; then
            pnpm run build
          elif [ "${{ steps.pm.outputs.pm }}" = "yarn" ]; then
            yarn build
          else
            npm run build
          fi

      # 验证构建结果
      - name: Verify build output
        run: |
          cd anzhiyu-blog
          if [ -d "public" ] && [ "$(ls -A public)" ]; then
            echo "✅ Build successful! Files generated:"
            ls -la public/ | head -10
            echo "OUT_DIR=anzhiyu-blog/public" >> $GITHUB_ENV
          else
            echo "❌ Build failed: public directory is empty or doesn't exist!"
            exit 1
          fi

      # 部署到服务器
      - name: Deploy to server
        uses: burnett01/rsync-deployments@6.0.0
        with:
          switches: -avzr --delete --exclude='.git*' --exclude='node_modules' --exclude='.github' --exclude='*.log'
          path: ${{ env.OUT_DIR }}/
          remote_path: ${{ secrets.TARGET_DIR }}
          remote_host: ${{ secrets.SSH_HOST }}
          remote_port: ${{ secrets.SSH_PORT }}
          remote_user: ${{ secrets.SSH_USER }}
          remote_key: ${{ secrets.SSH_PRIVATE_KEY }}

      # 部署后验证
      - name: Post-deployment verification
        run: |
          echo "🎉 Deployment completed!"
          echo "📍 Blog should be accessible at: http://${{ secrets.SSH_HOST }}"
          echo "📁 Files deployed to: ${{ secrets.TARGET_DIR }}"
