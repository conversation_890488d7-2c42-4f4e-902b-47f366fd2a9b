#!/bin/bash

# 部署后自动配置脚本
# 用于在GitHub Actions部署完成后自动配置Nginx

set -e

# 颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
WEB_ROOT="/var/www/blog/web"
NGINX_CONFIG="/etc/nginx/sites-available/blog"
SERVER_IP="************"

# 检查是否为root用户
if [[ $EUID -ne 0 ]]; then
    log_error "此脚本需要root权限运行"
    exit 1
fi

log_info "开始配置部署后环境..."

# 检查web目录是否存在
if [ ! -d "$WEB_ROOT" ]; then
    log_error "Web目录不存在: $WEB_ROOT"
    exit 1
fi

# 检查index.html是否存在
if [ ! -f "$WEB_ROOT/index.html" ]; then
    log_error "index.html不存在: $WEB_ROOT/index.html"
    exit 1
fi

log_info "Web文件检查通过"

# 配置Nginx
log_info "配置Nginx..."

cat > "$NGINX_CONFIG" << EOF
server {
    listen 80;
    server_name $SERVER_IP _;
    
    # 指向web目录中的静态文件
    root $WEB_ROOT;
    index index.html index.htm;
    
    # 静态文件处理
    location / {
        try_files \$uri \$uri/ /index.html;
    }
    
    # 静态资源缓存优化
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
    }
    
    # 安全头部
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    
    # 可选：源码访问
    location /source/ {
        alias /var/www/blog/;
        autoindex on;
    }
    
    # 日志
    access_log /var/log/nginx/blog.access.log;
    error_log /var/log/nginx/blog.error.log;
}
EOF

# 启用站点
ln -sf "$NGINX_CONFIG" /etc/nginx/sites-enabled/blog

# 删除默认站点（如果存在）
rm -f /etc/nginx/sites-enabled/default

# 测试Nginx配置
log_info "测试Nginx配置..."
if nginx -t; then
    log_info "Nginx配置测试通过"
else
    log_error "Nginx配置测试失败"
    exit 1
fi

# 设置正确的文件权限
log_info "设置文件权限..."
chown -R deploy:deploy /var/www/blog
chmod -R 755 /var/www/blog

# 重载Nginx
log_info "重载Nginx配置..."
systemctl reload nginx

# 检查Nginx状态
if systemctl is-active --quiet nginx; then
    log_info "Nginx运行正常"
else
    log_error "Nginx未运行"
    systemctl status nginx
    exit 1
fi

# 显示完成信息
echo ""
log_info "🎉 部署后配置完成！"
echo ""
echo "📊 配置信息："
echo "  • Web根目录: $WEB_ROOT"
echo "  • Nginx配置: $NGINX_CONFIG"
echo "  • 访问地址: http://$SERVER_IP"
echo ""
echo "🔍 验证命令："
echo "  • 检查文件: ls -la $WEB_ROOT"
echo "  • 查看日志: sudo tail -f /var/log/nginx/blog.access.log"
echo "  • 测试访问: curl -I http://$SERVER_IP"
echo ""

# 测试网站访问
log_info "测试网站访问..."
if curl -s -o /dev/null -w "%{http_code}" http://localhost | grep -q "200"; then
    log_info "✅ 网站访问正常"
else
    log_warning "⚠️  网站可能无法访问，请检查配置"
fi

log_info "配置完成！"
