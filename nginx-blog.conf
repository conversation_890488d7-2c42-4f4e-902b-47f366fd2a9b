# Nginx配置文件 - 博客站点
# 将此文件复制到 /etc/nginx/sites-available/blog
# 然后创建软链接: sudo ln -s /etc/nginx/sites-available/blog /etc/nginx/sites-enabled/

server {
    listen 80;
    listen [::]:80;
    
    # 修改为你的域名
    server_name your-domain.com www.your-domain.com;
    
    # 博客静态文件根目录
    root /var/www/html/blog;
    index index.html index.htm;
    
    # 日志文件
    access_log /var/log/nginx/blog_access.log;
    error_log /var/log/nginx/blog_error.log;
    
    # 主要位置配置
    location / {
        try_files $uri $uri/ =404;
        
        # 缓存静态文件
        location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Vary Accept-Encoding;
        }
        
        # HTML文件缓存策略
        location ~* \.html$ {
            expires 1h;
            add_header Cache-Control "public";
        }
    }
    
    # Webhook处理器（如果使用PHP版本）
    location /webhook-handler.php {
        include snippets/fastcgi-php.conf;
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock; # 根据PHP版本调整
        
        # 限制访问
        allow ************/20;   # GitHub IP范围
        allow *************/22;
        allow ************/22;
        allow ***********/20;
        deny all;
    }
    
    # 安全配置
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ ~$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 禁止访问敏感文件
    location ~* \.(yml|yaml|json|md|txt|log)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
}

# HTTPS配置（使用Let's Encrypt）
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    
    server_name your-domain.com www.your-domain.com;
    
    # SSL证书路径（使用certbot自动生成）
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    
    # SSL配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # HSTS
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # 其他安全头
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # 博客静态文件根目录
    root /var/www/html/blog;
    index index.html index.htm;
    
    # 日志文件
    access_log /var/log/nginx/blog_ssl_access.log;
    error_log /var/log/nginx/blog_ssl_error.log;
    
    # 复用HTTP配置
    include /etc/nginx/snippets/blog-common.conf;
}

# HTTP到HTTPS重定向
server {
    listen 80;
    listen [::]:80;
    
    server_name your-domain.com www.your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

# Webhook服务器配置（如果使用Node.js版本）
upstream webhook_backend {
    server 127.0.0.1:3001;
    keepalive 32;
}

server {
    listen 80;
    server_name webhook.your-domain.com;
    
    location / {
        proxy_pass http://webhook_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 限制访问
        allow ************/20;   # GitHub IP范围
        allow *************/22;
        allow ************/22;
        allow ***********/20;
        deny all;
    }
}
