// 头像颜色提取和应用
const avatarColor = async () => {
  const root = document.querySelector(":root");
  const avatarImg = document.querySelector(".avatar-img");
  
  if (!avatarImg || !GLOBAL_CONFIG.mainTone || !GLOBAL_CONFIG.mainTone.avatar_change) {
    return;
  }

  const avatarSrc = avatarImg.src;
  if (!avatarSrc) {
    return;
  }

  console.log("开始提取头像颜色:", avatarSrc);

  try {
    let fetchPath = "";
    
    // 根据配置选择API模式
    if (GLOBAL_CONFIG.mainTone.mode == "cdn" || GLOBAL_CONFIG.mainTone.mode == "both") {
      fetchPath = avatarSrc + "?imageAve";
    } else if (GLOBAL_CONFIG.mainTone.mode == "api") {
      fetchPath = GLOBAL_CONFIG.mainTone.api + avatarSrc;
    }

    console.log("请求颜色API:", fetchPath);

    const response = await fetch(fetchPath);
    if (response.ok && response.headers.get("content-type")?.includes("application/json")) {
      const obj = await response.json();
      let value = GLOBAL_CONFIG.mainTone.mode == "cdn" || GLOBAL_CONFIG.mainTone.mode == "both"
        ? "#" + obj.RGB.slice(2)
        : obj.RGB;

      console.log("提取到的头像颜色:", value);

      // 如果颜色太亮，稍微调暗一些
      if (getContrastYIQ(value) === "light") {
        value = LightenDarkenColor(colorHex(value), -20);
      }

      // 应用头像颜色到CSS变量
      applyAvatarColor(value);
      
    } else if (GLOBAL_CONFIG.mainTone.mode == "both") {
      // both模式失败后尝试API
      try {
        const apiResponse = await fetch(GLOBAL_CONFIG.mainTone.api + avatarSrc);
        if (apiResponse.ok && apiResponse.headers.get("content-type")?.includes("application/json")) {
          const obj = await apiResponse.json();
          let value = obj.RGB;

          if (getContrastYIQ(value) === "light") {
            value = LightenDarkenColor(colorHex(value), -20);
          }

          console.log("API模式提取到的头像颜色:", value);
          applyAvatarColor(value);
        }
      } catch (error) {
        console.log("头像颜色提取失败 (API模式):", error);
      }
    }
  } catch (error) {
    console.log("头像颜色提取失败:", error);
  }
};

// 应用头像颜色到页面
const applyAvatarColor = (color) => {
  const root = document.documentElement;

  // 设置主色调
  root.style.setProperty("--anzhiyu-main", color);

  // 设置透明度变体
  const colorWithOpacity23 = color + "23";
  const colorWithOpacityDD = color + "dd";

  root.style.setProperty("--anzhiyu-theme-op", colorWithOpacity23);
  root.style.setProperty("--anzhiyu-theme-op-deep", colorWithOpacityDD);

  // 设置主题色
  root.style.setProperty("--anzhiyu-theme", color);

  // 强制更新头像卡片背景（确保暗色模式下也生效）
  const cardInfo = document.querySelector('#aside-content > .card-widget.card-info');
  if (cardInfo) {
    // 临时移除并重新添加类来触发重绘
    cardInfo.style.setProperty('--avatar-dynamic-color', color);

    // 直接设置伪元素样式（通过CSS变量）
    root.style.setProperty('--avatar-bg-gradient',
      `linear-gradient(-25deg, ${color}, ${colorWithOpacityDD}, ${color}, ${colorWithOpacityDD})`);
  }

  console.log("头像颜色已应用:", {
    main: color,
    op: colorWithOpacity23,
    opDeep: colorWithOpacityDD
  });

  // 触发主题色更新
  requestAnimationFrame(() => {
    if (typeof anzhiyu !== 'undefined' && anzhiyu.initThemeColor) {
      anzhiyu.initThemeColor();
    }

    // 强制重绘头像卡片
    const cardInfo = document.querySelector('#aside-content > .card-widget.card-info');
    if (cardInfo) {
      cardInfo.style.transform = 'translateZ(0)';
      setTimeout(() => {
        cardInfo.style.transform = '';
      }, 10);
    }
  });
};

// 颜色对比度检测函数（如果main.js中没有的话）
if (typeof getContrastYIQ === 'undefined') {
  window.getContrastYIQ = function(hexcolor) {
    const r = parseInt(hexcolor.substr(1, 2), 16);
    const g = parseInt(hexcolor.substr(3, 2), 16);
    const b = parseInt(hexcolor.substr(5, 2), 16);
    const yiq = ((r * 299) + (g * 587) + (b * 114)) / 1000;
    return (yiq >= 128) ? 'light' : 'dark';
  };
}

// 颜色调亮调暗函数（如果main.js中没有的话）
if (typeof LightenDarkenColor === 'undefined') {
  window.LightenDarkenColor = function(col, amt) {
    let usePound = false;
    if (col[0] == "#") {
      col = col.slice(1);
      usePound = true;
    }
    const num = parseInt(col, 16);
    let r = (num >> 16) + amt;
    if (r > 255) r = 255;
    else if (r < 0) r = 0;
    let b = ((num >> 8) & 0x00FF) + amt;
    if (b > 255) b = 255;
    else if (b < 0) b = 0;
    let g = (num & 0x0000FF) + amt;
    if (g > 255) g = 255;
    else if (g < 0) g = 0;
    return (usePound ? "#" : "") + (g | (b << 8) | (r << 16)).toString(16);
  };
}

// 颜色转换函数（如果main.js中没有的话）
if (typeof colorHex === 'undefined') {
  window.colorHex = function(color) {
    const div = document.createElement('div');
    div.style.color = color;
    document.body.appendChild(div);
    const rgbColor = window.getComputedStyle(div).color;
    document.body.removeChild(div);
    
    const rgb = rgbColor.match(/\d+/g);
    if (rgb) {
      return "#" + ((1 << 24) + (parseInt(rgb[0]) << 16) + (parseInt(rgb[1]) << 8) + parseInt(rgb[2])).toString(16).slice(1);
    }
    return color;
  };
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', () => {
  // 延迟执行，确保头像已加载
  setTimeout(() => {
    avatarColor();
  }, 1000);
});

// 如果头像图片加载完成后再执行
const avatarImg = document.querySelector('.avatar-img');
if (avatarImg) {
  if (avatarImg.complete) {
    setTimeout(() => {
      avatarColor();
    }, 500);
  } else {
    avatarImg.addEventListener('load', () => {
      setTimeout(() => {
        avatarColor();
      }, 500);
    });
  }
}
