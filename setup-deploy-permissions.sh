#!/bin/bash

# 设置deploy用户权限脚本
# 需要在服务器上以root身份运行一次

set -e

echo "🔧 设置deploy用户权限..."

# 给deploy用户sudo权限执行特定命令
cat > /etc/sudoers.d/deploy << 'EOF'
# 允许deploy用户执行部署相关命令
deploy ALL=(ALL) NOPASSWD: /var/www/blog/post-deploy-config.sh
deploy ALL=(ALL) NOPASSWD: /bin/systemctl reload nginx
deploy ALL=(ALL) NOPASSWD: /bin/systemctl status nginx
deploy ALL=(ALL) NOPASSWD: /usr/sbin/nginx -t
deploy ALL=(ALL) NOPASSWD: /bin/chown -R deploy:deploy /var/www/blog
deploy ALL=(ALL) NOPASSWD: /bin/chmod -R 755 /var/www/blog
EOF

# 设置正确的权限
chmod 440 /etc/sudoers.d/deploy

# 给配置脚本执行权限
chmod +x /var/www/blog/post-deploy-config.sh 2>/dev/null || echo "配置脚本将在下次部署时创建"

echo "✅ deploy用户权限设置完成"
echo ""
echo "deploy用户现在可以执行："
echo "  • sudo /var/www/blog/post-deploy-config.sh"
echo "  • sudo systemctl reload nginx"
echo "  • sudo nginx -t"
echo ""
echo "请确保在下次GitHub Actions部署后，网站将自动配置！"
